{"APDS9960_Interface": {"status_enum": {"APDS9960_StatusTypeDef": ["APDS9960_OK", "APDS9960_ERROR_I2C", "APDS9960_ERROR_INVALID_MODE", "APDS9960_ERROR_DATA_OVERRUN", "APDS9960_ERROR_TIMEOUT"]}, "functions": [{"name": "apds9960_init", "description": "初始化APDS-9960（配置I2C通信+读取设备ID+基础参数）", "return_type": "APDS9960_StatusTypeDef", "parameters": [{"name": "i2c_port", "type": "uint8_t", "description": "ESP32的I2C端口号（I2C_NUM_0/I2C_NUM_1）"}, {"name": "sda_pin", "type": "uint8_t", "description": "I2C数据引脚编号（如GPIO21）"}, {"name": "scl_pin", "type": "uint8_t", "description": "I2C时钟引脚编号（如GPIO22）"}, {"name": "i2c_addr", "type": "uint8_t", "description": "I2C设备地址（0x39或0x33，由ADDR引脚决定）"}]}, {"name": "apds9960_set_mode", "description": "设置工作模式（环境光/颜色/接近检测）", "return_type": "APDS9960_StatusTypeDef", "parameters": [{"name": "mode", "type": "apds9960_mode_t", "description": "工作模式枚举（OFF/AMBIENT/PROXIMITY/COLOR）"}, {"name": "enable", "type": "bool", "description": "是否启用该模式（true=启用，false=禁用）"}]}, {"name": "apds9960_read_ambient_light", "description": "读取环境光强度（原始ADC值或校准后的lux值）", "return_type": "APDS9960_StatusTypeDef", "parameters": [{"name": "raw_output", "type": "bool", "description": "是否返回原始ADC值（true=原始值，false=校准后值）"}, {"name": "light_adc", "type": "uint16_t*", "description": "输出参数（仅raw_output=true时有效，原始ADC值）"}, {"name": "light_lux", "type": "float*", "description": "输出参数（仅raw_output=false时有效，校准后lux值）"}]}, {"name": "apds9960_read_color_rgb", "description": "读取RGB颜色值（原始ADC值或校准后的RGB强度）", "return_type": "APDS9960_StatusTypeDef", "parameters": [{"name": "raw_output", "type": "bool", "description": "是否返回原始ADC值（true=原始值，false=校准后值）"}, {"name": "raw_data", "type": "apds9960_raw_data_t*", "description": "输出参数（仅raw_output=true时有效，存储原始RGB ADC值）"}, {"name": "color_result", "type": "apds9960_color_result_t*", "description": "输出参数（仅raw_output=false时有效，存储校准后颜色结果）"}]}, {"name": "apds9960_read_proximity", "description": "读取接近物体的距离（原始ADC值，需校准转换为实际距离）", "return_type": "APDS9960_StatusTypeDef", "parameters": [{"name": "raw_output", "type": "bool", "description": "是否返回原始ADC值（true=原始值，false=校准后值）"}, {"name": "prox_adc", "type": "uint16_t*", "description": "输出参数（仅raw_output=true时有效，原始ADC值）"}, {"name": "prox_distance", "type": "uint8_t*", "description": "输出参数（仅raw_output=false时有效，校准后距离，单位：cm）"}]}, {"name": "apds9960_soft_reset", "description": "通过写入RESET寄存器触发传感器软复位", "return_type": "APDS9960_StatusTypeDef", "parameters": []}], "data_types": {"apds9960_mode_t": ["APDS9960_MODE_OFF", "APDS9960_MODE_AMBIENT", "APDS9960_MODE_PROXIMITY", "APDS9960_MODE_COLOR"], "apds9960_raw_data_t": {"light_adc": "uint16_t", "red_adc": "uint16_t", "green_adc": "uint16_t", "blue_adc": "uint16_t"}, "apds9960_color_result_t": {"r": "uint16_t", "g": "uint16_t", "b": "uint16_t", "cct": "uint16_t"}}, "hardware_config": {"i2c_port": 0, "sda_pin": 21, "scl_pin": 22, "i2c_addr": "0x39", "interrupt_pin": 18}, "error_handling": {"retry_count": 3, "timeout_threshold": 100}}}