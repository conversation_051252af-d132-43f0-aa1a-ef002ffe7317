{"AT89C52LED_Interface": {"status_enum": {"at89c52_digital_tube_status_t": ["AT89C52_DIGITAL_TUBE_OK", "AT89C52_DIGITAL_TUBE_ERROR_I2C", "AT89C52_DIGITAL_TUBE_ERROR_TIMEOUT", "AT89C52_DIGITAL_TUBE_ERROR_INVALID_DATA", "AT89C52_DIGITAL_TUBE_ERROR_NOT_INITIALIZED", "AT89C52_DIGITAL_TUBE_ERROR_DISPLAY_OVERRUN", "AT89C52_DIGITAL_TUBE_ERROR_BRIGHTNESS_OUT_OF_RANGE"]}, "functions": [{"name": "at89c52_digital_tube_init", "return_type": "at89c52_digital_tube_status_t", "parameters": [{"name": "i2c_port", "type": "uint8_t", "description": "I2C端口号（如I2C_NUM_0）"}, {"name": "i2c_addr", "type": "uint8_t", "description": "I2C设备地址（0x20-0x27，默认0x20）"}, {"name": "i2c_clk_hz", "type": "uint32_t", "description": "I2C时钟频率（单位：Hz，默认100000）"}, {"name": "brightness", "type": "uint8_t", "description": "初始亮度（0-100%，默认50%）"}]}, {"name": "at89c52_digital_tube_display_number", "return_type": "at89c52_digital_tube_status_t", "parameters": [{"name": "position", "type": "uint8_t", "description": "显示位置（0-7，对应8位数码管）"}, {"name": "number", "type": "uint8_t", "description": "要显示的数字（0-9）"}, {"name": "dot", "type": "bool", "description": "是否点亮小数点（true=点亮，false=关闭）"}]}, {"name": "at89c52_digital_tube_display_char", "return_type": "at89c52_digital_tube_status_t", "parameters": [{"name": "position", "type": "uint8_t", "description": "显示位置（0-7）"}, {"name": "char_code", "type": "uint8_t", "description": "字符编码（ASCII码，支持自定义字符）"}]}, {"name": "at89c52_digital_tube_clear", "return_type": "at89c52_digital_tube_status_t", "parameters": []}, {"name": "at89c52_digital_tube_set_brightness", "return_type": "at89c52_digital_tube_status_t", "parameters": [{"name": "brightness", "type": "uint8_t", "description": "亮度值（0-100%）"}]}, {"name": "at89c52_digital_tube_shutdown", "return_type": "at89c52_digital_tube_status_t", "parameters": []}, {"name": "at89c52_digital_tube_wakeup", "return_type": "at89c52_digital_tube_status_t", "parameters": []}], "data_types": {"at89c52_digital_tube_display_buffer_t": {"buffer": "uint8_t[8]"}, "at89c52_digital_tube_bright_param_t": {"brightness": "uint8_t"}}, "hardware_config": {"default_i2c_port": "I2C_NUM_0", "default_i2c_addr": "0x20", "default_i2c_clk_hz": 100000, "default_brightness": 50, "brightness_range": {"min": 0, "max": 100}, "segment_count": 7, "max_positions": 8, "active_level": "LOW"}, "error_handling": {"retry_count": 3, "timeout_threshold_ms": 200}, "note": "AT89C52配套数码管为I2C接口8位数码管驱动模块，适配AT89C52单片机系统，支持共阴极7段数码管（含小数点）。通过I2C协议（0x20-0x27）与AT89C52通信，可控制单段显示、整体亮度及清屏操作。显示内容通过8位缓冲区管理，每位对应一个数码管位。亮度调节范围0-100%（PWM控制），支持数字（0-9）和ASCII字符显示。初始化需配置I2C参数及初始亮度，显示时需指定位置和内容。"}}