#!/usr/bin/env python3
"""
测试新的工作流ID生成逻辑
"""
import uuid
import time

def generate_workflow_id():
    """生成简短的工作流ID"""
    timestamp_suffix = str(int(time.time()))[-4:]  # 时间戳后4位
    random_suffix = str(uuid.uuid4())[:4]  # UUID前4位
    workflow_id = f"wf-{timestamp_suffix}-{random_suffix}"
    return workflow_id

# 测试生成几个工作流ID
print("测试新的工作流ID生成：")
print("=" * 40)
for i in range(5):
    wf_id = generate_workflow_id()
    print(f"工作流ID {i+1}: {wf_id} (长度: {len(wf_id)} 字符)")
    time.sleep(0.1)  # 稍微延迟确保时间戳不同

print("\n对比：")
print(f"旧格式: wf-{uuid.uuid4()} (长度: {len(f'wf-{uuid.uuid4()}')} 字符)")
print(f"新格式: {generate_workflow_id()} (长度: {len(generate_workflow_id())} 字符)")
