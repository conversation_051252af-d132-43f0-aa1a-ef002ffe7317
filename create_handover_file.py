# create_handover_file.py
# -*- coding: utf-8 -*-

import os
import textwrap

# =================================================================================
# 1. 更新后的文件清单 (根据最新的文件结构)
# =================================================================================
# 这个列表现在包含了项目的所有核心文件，并按照模块进行了分组
FILES_TO_INCLUDE = [
    # --- 根目录核心文件 ---
    'run.py',
    'config.py',

    # --- App 核心模块 ---
    'app/__init__.py',
    'app/models.py',

    # --- API 路由模块 ---
    'app/api/__init__.py',
    'app/api/auth_routes.py',
    'app/api/device_routes.py',
    'app/api/log_stream_routes.py',
    'app/api/mqtt_routes.py',  # <-- 新增 MQTT 路由
    'app/api/project_routes.py',
    'app/api/user_routes.py',
    'app/api/workflow_routes.py',

    # --- 服务层模块 ---
    'app/services/__init__.py',
    'app/services/auth_service.py',
    'app/services/device_service.py',
    'app/services/mqtt_service.py',  # <-- 新增 MQTT 服务
    'app/services/project_analyzer_service.py',
    'app/services/user_service.py',
    'app/services/syntax_analyzer_service.py',
    'app/services/workflow_service.py',

    # --- LangGraph 定义 ---
    'app/langgraph_def/__init__.py',
    'app/langgraph_def/agent_state.py',
    'app/langgraph_def/graph_builder.py',

    # --- 前端模板 ---
    'app/templates/index.html',

]

# =================================================================================
# 2. 交接备忘录
# =================================================================================
HANDOVER_MEMO = textwrap.dedent("""
""")



def create_handover_file():
    """
    主函数：创建交接单文件。
    """
    output_filename = "handover.txt"
    try:
        with open(output_filename, 'w', encoding='utf-8') as f_out:
            # 写入备忘录
            f_out.write(HANDOVER_MEMO)
            f_out.write("\\n\\n")

            # 写入所有文件内容
            for filepath in FILES_TO_INCLUDE:
                # 兼容Windows和Linux的路径分隔符
                normalized_path = os.path.join(*filepath.split('/'))
                separator = f"\\n--- FILE: {filepath} ---\\n"
                print(f"正在打包文件: {filepath}")
                f_out.write(separator)

                try:
                    with open(normalized_path, 'r', encoding='utf-8') as f_in:
                        f_out.write(f_in.read())
                except FileNotFoundError:
                    f_out.write(f"# <<< 文件未找到: {filepath} >>>")
                    print(f"  警告: 文件 {filepath} 未找到，已在交接单中标记。")

        print(f"\\n成功！项目交接单 '{output_filename}' 已生成在您的项目根目录。")
        print("交接单包含了项目的所有核心文件内容。")

    except Exception as e:
        print(f"\\n生成交接单时发生错误: {e}")


if __name__ == '__main__':
    create_handover_file()
