#!/usr/bin/env python3
"""
测试修复后的错误解析正则表达式
"""
import re
from pathlib import Path

def clean_module_filename(filename: str) -> str:
    """清理文件名，移除多重后缀（如 .ino.cpp）并返回模块ID"""
    for ext in ('.ino.cpp', '.cpp', '.c', '.cc', '.cxx', '.ino'):
        if filename.endswith(ext):
            return filename[:-len(ext)]
    return Path(filename).stem

def test_error_parsing(feedback: str, test_name: str):
    print(f"\n=== 测试 {test_name} ===")
    print(f"输入日志: {feedback}")
    
    faulty_module_id = None
    
    # 主要推断模式：从错误行直接解析文件路径
    primary_pattern = re.compile(
        r'^(?P<filepath>(?:[A-Za-z]:)?(?:.*?[/\\])?(?:src|lib)[/\\][^/\\:\n]+\.(?:c|cc|cpp|cxx|ino)(?:\.cpp)?)'
        r':\d+(?::\d+)?:\s+(?:fatal\s+)?error:', 
        re.MULTILINE | re.IGNORECASE
    )
    match = primary_pattern.search(feedback)
    
    if match:
        filepath = Path(match.group('filepath'))
        faulty_module_id = clean_module_filename(filepath.name)
        print(f"✅ 主模式匹配: '{filepath}' -> '{faulty_module_id}'")
        return faulty_module_id
    else:
        print("❌ 主模式未匹配")
    
    # 备用推断模式：从各种构建系统的失败汇总行解析
    fallback_pattern = re.compile(
        r"\*\*\*\s*\[.*?[/\\](?:src|lib)[/\\].*?([^\s/\\]+?)\.(?:c|cc|cpp|cxx|ino)(?:\.cpp)?\.o\]\s+Error\b",
        re.MULTILINE | re.IGNORECASE
    )
    match = fallback_pattern.search(feedback)
    if match:
        faulty_module_id_with_ext = match.group(1)
        faulty_module_id = clean_module_filename(faulty_module_id_with_ext)
        print(f"✅ 备用模式匹配: '{faulty_module_id_with_ext}' -> '{faulty_module_id}'")
        return faulty_module_id
    else:
        print("❌ 备用模式未匹配")
    
    # 兜底模式：匹配"Compiling"行
    compiling_pattern = re.compile(
        r"Compiling\s+.*?[/\\](?:src|lib)[/\\].*?([^\s/\\]+?)\.(?:c|cc|cpp|cxx|ino)(?:\.cpp)?\.o",
        re.IGNORECASE
    )
    match = compiling_pattern.search(feedback)
    if match:
        faulty_module_id = clean_module_filename(match.group(1))
        print(f"✅ 兜底模式匹配: '{faulty_module_id}'")
        return faulty_module_id
    else:
        print("❌ 兜底模式未匹配")
    
    print("❌ 所有模式都未匹配，将默认为 app_main")
    return "app_main"

# 测试用例
test_cases = [
    # 你的实际错误日志
    ("src/bh1750_driver.cpp:123:16: error: 'NAN' was not declared in this scope", "实际错误日志"),
    
    # 带路径前缀的情况
    ("C:/path/to/project/src/bh1750_driver.cpp:123:16: error: 'NAN' was not declared in this scope", "带完整路径"),
    
    # fatal error 情况
    ("src/sensor_driver.cpp:45:10: fatal error: math.h: No such file or directory", "fatal error"),
    
    # 备用模式测试 - .bld 构建目录
    ("*** [.bld\\dev_f29d\\src\\bh1750_driver.cpp.o] Error 1", "新构建目录 .bld"),
    
    # 备用模式测试 - 传统 .pio 构建目录
    ("*** [.pio\\build\\esp32dev\\src\\sensor_driver.cpp.o] Error 1", "传统 .pio 构建目录"),
    
    # 兜底模式测试
    ("Compiling .bld\\dev_f29d\\src\\temperature_sensor.cpp.o", "Compiling 行"),
    
    # .ino.cpp 多后缀测试
    ("src/app_main.ino.cpp:67:5: error: 'logger' was not declared in this scope", "多后缀 .ino.cpp"),
]

for feedback, test_name in test_cases:
    result = test_error_parsing(feedback, test_name)
    print(f"结果: {result}")
