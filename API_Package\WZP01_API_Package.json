{"WZP01_Interface": {"status_enum": {"wzp01_status_t": ["WZP01_OK", "WZP01_ERROR_I2C", "WZP01_ERROR_TIMEOUT", "WZP01_ERROR_INVALID_DATA", "WZP01_ERROR_NOT_INITIALIZED", "WZP01_ERROR_OVER_TEMPERATURE", "WZP01_ERROR_UNDER_TEMPERATURE", "WZP01_ERROR_CALIBRATION_FAIL"]}, "functions": [{"name": "wzp01_init", "return_type": "wzp01_status_t", "parameters": [{"name": "i2c_port", "type": "uint8_t", "description": "I2C端口号（如I2C_NUM_0）"}, {"name": "i2c_addr", "type": "uint8_t", "description": "I2C设备地址（0x48/0x49，默认0x48）"}, {"name": "i2c_clk_hz", "type": "uint32_t", "description": "I2C时钟频率（单位：Hz，默认400000）"}, {"name": "power_pin", "type": "int8_t", "description": "电源控制引脚（-1=禁用，默认-1）"}, {"name": "reset_pin", "type": "int8_t", "description": "复位引脚（-1=禁用，默认-1）"}]}, {"name": "wzp01_read_temperature", "return_type": "wzp01_status_t", "parameters": [{"name": "temperature_c", "type": "float*", "description": "输出参数（温度值，单位：°C）"}]}, {"name": "wzp01_read_raw", "return_type": "wzp01_status_t", "parameters": [{"name": "raw_data", "type": "int16_t*", "description": "输出参数（16位原始铂电阻值）"}]}, {"name": "wzp01_set_resolution", "return_type": "wzp01_status_t", "parameters": [{"name": "resolution_bits", "type": "uint8_t", "description": "温度分辨率（0x09=9位, 0x0A=10位, 0x0B=11位, 0x0C=12位）"}]}, {"name": "wzp01_set_operating_mode", "return_type": "wzp01_status_t", "parameters": [{"name": "mode", "type": "uint8_t", "description": "工作模式（0=连续转换，1=单次转换后休眠）"}]}, {"name": "wzp01_calibrate", "return_type": "wzp01_status_t", "parameters": [{"name": "ref_temp_c", "type": "float", "description": "参考温度值（单位：°C）"}, {"name": "ref_raw", "type": "int16_t", "description": "参考温度对应的原始铂电阻值"}]}, {"name": "wzp01_soft_reset", "return_type": "wzp01_status_t", "parameters": []}, {"name": "wzp01_power_on", "return_type": "wzp01_status_t", "parameters": []}, {"name": "wzp01_power_off", "return_type": "wzp01_status_t", "parameters": []}], "data_types": {"wzp01_raw_data_t": {"raw_pt": "int16_t"}, "wzp01_calib_param_t": {"ref_temp_c": "float", "ref_raw": "int16_t", "slope": "float"}}, "hardware_config": {"default_i2c_port": "I2C_NUM_0", "default_i2c_addr": "0x48", "default_i2c_clk_hz": 400000, "default_resolution": "0x0C", "default_operating_mode": 0, "power_pin": -1, "reset_pin": -1, "temp_range_degc": {"min": -50, "max": 150}, "base_resolution_degc": 0.1}, "error_handling": {"retry_count": 3, "timeout_threshold_ms": 200}, "note": "WZP-01为铂电阻温度传感器，基于RTD（电阻温度探测器）原理，通过I2C接口（0x48/0x49）输出铂电阻值。测量范围-50°C~150°C，核心原理为铂电阻阻值随温度变化的特性（PT100/PT1000可选）。原始数据为16位有符号整数（单位：0.1Ω），需通过校准参数（ref_temp_c/ref_raw/slope）转换为实际温度（temperature_c = (raw_pt - ref_raw) * slope + ref_temp_c）。支持积分时间调节和连续/单次测量模式，校准需使用已知参考温度点修正非线性误差。"}}