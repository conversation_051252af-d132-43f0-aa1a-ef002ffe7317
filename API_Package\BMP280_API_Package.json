{"BMP280_Interface": {"status_enum": {"BMP280_StatusTypeDef": ["BMP280_OK", "BMP280_ERROR_I2C", "BMP280_ERROR_SPI", "BMP280_ERROR_REG_READ", "BMP280_ERROR_REG_WRITE", "BMP280_ERROR_TIMEOUT", "BMP280_ERROR_CALIBRATION_DATA", "BMP280_ERROR_INVALID_OS"]}, "functions": [{"name": "bmp280_init", "description": "初始化BMP280传感器（配置通信接口+读取设备ID+加载校准参数）", "return_type": "BMP280_StatusTypeDef", "parameters": [{"name": "comm_mode", "type": "bmp280_comm_mode_t", "description": "通信模式（I2C_MODE/SPI_MODE）"}, {"name": "i2c_port", "type": "uint8_t", "description": "I2C端口号（仅I2C_MODE有效，如I2C_NUM_0）"}, {"name": "sda_pin", "type": "uint8_t", "description": "I2C数据引脚编号（仅I2C_MODE有效，如GPIO21）"}, {"name": "scl_pin", "type": "uint8_t", "description": "I2C时钟引脚编号（仅I2C_MODE有效，如GPIO22）"}, {"name": "spi_cs_pin", "type": "uint8_t", "description": "SPI片选引脚编号（仅SPI_MODE有效，如GPIO18）"}, {"name": "i2c_addr", "type": "uint8_t", "description": "I2C设备地址（0x76=BSL引脚接地，0x77=BSL引脚接VDD，仅I2C_MODE有效）"}, {"name": "spi_3wire", "type": "bool", "description": "SPI是否使用3线模式（默认4线，仅SPI_MODE有效）"}]}, {"name": "bmp280_set_osr", "description": "设置气压/温度过采样率（影响精度与转换时间）", "return_type": "BMP280_StatusTypeDef", "parameters": [{"name": "pressure_osr", "type": "bmp280_osr_t", "description": "气压过采样率（0=跳过，1x/2x/4x/8x/16x）"}, {"name": "temperature_osr", "type": "bmp280_osr_t", "description": "温度过采样率（0=跳过，1x/2x/4x/8x/16x）"}]}, {"name": "bmp280_read_raw_pressure", "description": "读取气压原始ADC值（20位，需等待转换完成）", "return_type": "BMP280_StatusTypeDef", "parameters": [{"name": "raw_pressure", "type": "uint32_t*", "description": "输出参数（气压原始值，单位：LSB）"}]}, {"name": "bmp280_read_raw_temperature", "description": "读取温度原始ADC值（20位，需等待转换完成）", "return_type": "BMP280_StatusTypeDef", "parameters": [{"name": "raw_temperature", "type": "uint32_t*", "description": "输出参数（温度原始值，单位：LSB）"}]}, {"name": "bmp280_read_pressure", "description": "读取校准后的气压值（单位：hPa，自动补偿温度）", "return_type": "float", "parameters": []}, {"name": "bmp280_read_temperature", "description": "读取校准后的温度值（单位：°C，已补偿）", "return_type": "float", "parameters": []}, {"name": "bmp280_calculate_altitude", "description": "根据气压值计算海拔高度（需已知海平面气压）", "return_type": "float", "parameters": [{"name": "sea_level_pressure", "type": "float", "description": "海平面气压（单位：hPa）"}]}, {"name": "bmp280_soft_reset", "description": "通过写入RESET寄存器（0xE0）触发传感器软复位（恢复默认配置）", "return_type": "BMP280_StatusTypeDef", "parameters": []}, {"name": "bmp280_set_power_mode", "description": "设置传感器工作模式（睡眠/强制/正常）", "return_type": "BMP280_StatusTypeDef", "parameters": [{"name": "mode", "type": "bmp280_power_mode_t", "description": "工作模式（SLEEP_MODE/FORCED_MODE/NORMAL_MODE）"}]}, {"name": "bmp280_get measuring_duration", "description": "获取当前OSR配置下的测量耗时（用于同步等待）", "return_type": "uint32_t", "parameters": []}], "data_types": {"bmp280_comm_mode_t": ["BMP280_I2C_MODE", "BMP280_SPI_MODE"], "bmp280_osr_t": ["BMP280_OSR_SKIP", "BMP280_OSR_1X", "BMP280_OSR_2X", "BMP280_OSR_4X", "BMP280_OSR_8X", "BMP280_OSR_16X"], "bmp280_power_mode_t": ["BMP280_SLEEP_MODE", "BMP280_FORCED_MODE", "BMP280_NORMAL_MODE"], "bmp280_raw_data_t": {"pressure_adc": "uint32_t", "temperature_adc": "uint32_t"}, "bmp280_calib_param_t": {"dig_T1": "uint16_t", "dig_T2": "int16_t", "dig_T3": "int16_t", "dig_P1": "uint16_t", "dig_P2": "int16_t", "dig_P3": "int16_t", "dig_P4": "int16_t", "dig_P5": "int16_t", "dig_P6": "int16_t", "dig_P7": "int16_t", "dig_P8": "int16_t", "dig_P9": "int16_t"}}, "hardware_config": {"comm_mode": "BMP280_I2C_MODE", "i2c_port": 0, "sda_pin": 21, "scl_pin": 22, "i2c_addr": "0x76", "spi_cs_pin": 18, "i2c_frequency": 100000, "spi_frequency": 1000000, "power_pin": -1, "interrupt_pin": -1, "filter_coeff": "BMP280_FILTER_OFF"}, "error_handling": {"retry_count": 3, "timeout_threshold": 100}, "calibration_note": "传感器内部集成32字节EEPROM存储校准系数（dig_T1~dig_P9），初始化时自动读取并用于温度补偿和气压计算。温度补偿采用二阶多项式拟合，气压补偿结合过采样率和滤波器参数进行误差修正。具体公式可参考BMP280数据手册第8章。"}}