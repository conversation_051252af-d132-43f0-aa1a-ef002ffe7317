#!/usr/bin/env python3
"""
测试新的文件夹和环境命名逻辑
"""
import re

def generate_short_names(device_id: str, device_role: str) -> tuple[str, str]:
    """
    生成简短但可读的文件夹名和环境名，避免 Windows 路径长度限制。
    
    Args:
        device_id: 设备的内部ID
        device_role: 设备角色描述
        
    Returns:
        tuple: (文件夹名, 环境名)
    """
    project_prefix = "zygo"
    
    # 提取设备角色的关键词并缩写
    role_keywords = re.findall(r'[a-zA-Z]+', device_role.lower())
    if role_keywords:
        if len(role_keywords) >= 2:
            role_abbrev = role_keywords[0][:2] + role_keywords[1][:2]  # 如 "light sensor" -> "lise"
        else:
            role_abbrev = role_keywords[0][:4]  # 如 "controller" -> "cont"
    else:
        role_abbrev = "dev"  # 默认缩写
    
    # 取设备ID的后4位作为唯一标识
    device_suffix = device_id[-4:] if len(device_id) >= 4 else device_id
    
    # 组合成最终的名称
    folder_name = f"{project_prefix}_{role_abbrev}_{device_suffix}"
    env_name = f"{role_abbrev}_{device_suffix}"  # 环境名更短
    
    return folder_name, env_name

# 测试用例
test_cases = [
    ("65b275d4-dbe1-491b-bab7-1de2ab72f29d", "Light Sensor"),
    ("a1b2c3d4-e5f6-7890-abcd-ef1234567890", "Temperature Controller"),
    ("12345678-9abc-def0-1234-567890abcdef", "Motion Detector"),
    ("short-id", "Smart Switch"),
    ("xyz", "Device"),
    ("very-long-device-id-that-might-cause-issues", "Environmental Monitor System"),
]

print("测试新的命名逻辑：")
print("=" * 80)
for device_id, device_role in test_cases:
    folder_name, env_name = generate_short_names(device_id, device_role)
    print(f"设备ID: {device_id}")
    print(f"设备角色: {device_role}")
    print(f"文件夹名: {folder_name}")
    print(f"环境名: {env_name}")
    print(f"路径长度: {len(folder_name)} 字符")
    print("-" * 40)
